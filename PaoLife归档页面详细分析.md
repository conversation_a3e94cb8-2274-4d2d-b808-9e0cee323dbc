# PaoLife项目详细技术文档

## 第九部分：归档页面详细分析

### 页面概览

归档页面是PaoLife应用的数据管理核心，实现了项目、领域、资源的完整归档生命周期管理。包含归档列表展示、分类统计、搜索过滤、恢复操作和永久删除等功能，确保用户可以有效管理已完成或不再活跃的内容。

### 核心架构设计

#### 1. 归档数据结构

**归档项接口**:
```typescript
export interface ArchiveItem {
  id: string
  title: string
  description?: string
  type: 'project' | 'area' | 'resource'
  originalStatus: string
  archivedAt: string
  archivedReason: 'completed' | 'cancelled' | 'inactive' | 'outdated' | 'manual'
  tags: string[]
  metadata: {
    originalId: string
    completionRate?: number
    lastActivity?: string
    size?: number
    attachments?: number
  }
}
```

**归档原因分类**:
```typescript
const ArchiveReasons = {
  COMPLETED: 'completed',     // 已完成
  CANCELLED: 'cancelled',     // 已取消
  INACTIVE: 'inactive',       // 不活跃
  OUTDATED: 'outdated',       // 已过时
  MANUAL: 'manual'            // 手动归档
} as const

// 归档原因颜色映射
const getReasonColor = (reason: string) => {
  const colors = {
    'completed': 'text-green-600 bg-green-50 border-green-200',
    'cancelled': 'text-red-600 bg-red-50 border-red-200',
    'inactive': 'text-yellow-600 bg-yellow-50 border-yellow-200',
    'outdated': 'text-gray-600 bg-gray-50 border-gray-200',
    'manual': 'text-blue-600 bg-blue-50 border-blue-200'
  }
  return colors[reason] || colors.manual
}

// 归档原因标签
const getReasonLabel = (reason: string) => {
  const labels = {
    'completed': '已完成',
    'cancelled': '已取消',
    'inactive': '不活跃',
    'outdated': '已过时',
    'manual': '手动归档'
  }
  return labels[reason] || reason
}
```

#### 2. 归档页面组件 (ArchivePage.tsx)

**页面状态管理**:
```typescript
export function ArchivePage() {
  const [items, setItems] = useState<ArchiveItemType[]>([])
  const [searchQuery, setSearchQuery] = useState('')
  const [filterType, setFilterType] = useState<string>('all')
  const [filterReason, setFilterReason] = useState<string>('all')
  const [sortBy, setSortBy] = useState<string>('archivedAt')
  const [isLoading, setIsLoading] = useState(true)

  // 归档数据刷新
  const refreshArchivedItems = async () => {
    setIsLoading(true)
    try {
      const [projectsResult, areasResult] = await Promise.all([
        databaseApi.getArchivedProjects(),
        databaseApi.getArchivedAreas()
      ])

      const archivedItems: ArchiveItemType[] = []

      // 处理归档项目
      if (projectsResult.success && projectsResult.data) {
        const projectItems: ArchiveItemType[] = projectsResult.data.map((project: any) => ({
          id: project.id,
          title: project.name,
          description: project.description || '',
          type: 'project' as const,
          originalStatus: project.status || 'Unknown',
          archivedAt: project.updatedAt,
          archivedReason: 'manual' as const,
          tags: [],
          metadata: {
            originalId: project.id,
            lastActivity: project.updatedAt
          }
        }))
        archivedItems.push(...projectItems)
      }

      // 处理归档领域
      if (areasResult.success && areasResult.data) {
        const areaItems: ArchiveItemType[] = areasResult.data.map((area: any) => ({
          id: area.id,
          title: area.name,
          description: area.description || '',
          type: 'area' as const,
          originalStatus: area.status || 'Unknown',
          archivedAt: area.updatedAt,
          archivedReason: 'manual' as const,
          tags: [],
          metadata: {
            originalId: area.id,
            lastActivity: area.updatedAt
          }
        }))
        archivedItems.push(...areaItems)
      }

      setItems(archivedItems)
    } catch (error) {
      console.error('Failed to load archived items:', error)
      addNotification({
        type: 'error',
        title: '加载失败',
        message: '归档数据加载失败，请重试'
      })
    } finally {
      setIsLoading(false)
    }
  }

  // 计算分类统计
  const counts = useMemo(() => {
    return {
      projects: items.filter(item => item.type === 'project').length,
      areas: items.filter(item => item.type === 'area').length,
      resources: items.filter(item => item.type === 'resource').length
    }
  }, [items])

  // 过滤和排序
  const filteredItems = useMemo(() => {
    let filtered = items

    // 搜索过滤
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase()
      filtered = filtered.filter(item =>
        item.title.toLowerCase().includes(query) ||
        item.description?.toLowerCase().includes(query)
      )
    }

    // 类型过滤
    if (filterType !== 'all') {
      filtered = filtered.filter(item => item.type === filterType)
    }

    // 归档原因过滤
    if (filterReason !== 'all') {
      filtered = filtered.filter(item => item.archivedReason === filterReason)
    }

    // 排序
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'archivedAt':
          return new Date(b.archivedAt).getTime() - new Date(a.archivedAt).getTime()
        case 'title':
          return a.title.localeCompare(b.title)
        case 'type':
          return a.type.localeCompare(b.type)
        default:
          return 0
      }
    })

    return filtered
  }, [items, searchQuery, filterType, filterReason, sortBy])

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* 页面标题 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold">归档管理</h1>
          <p className="text-muted-foreground">
            管理已完成、取消或不再活跃的项目和领域
          </p>
        </div>
        <Button onClick={refreshArchivedItems} disabled={isLoading}>
          <RefreshCw className={cn("h-4 w-4 mr-2", isLoading && "animate-spin")} />
          刷新
        </Button>
      </div>

      {/* 分类统计卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Badge className="para-project">P</Badge>
              归档项目
            </CardTitle>
            <CardDescription>已完成或取消的项目</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{counts.projects}</div>
            <p className="text-xs text-muted-foreground">
              {counts.projects > 0 ? '最近有归档项目' : '暂无归档项目'}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Badge className="para-area">A</Badge>
              归档领域
            </CardTitle>
            <CardDescription>不再维护的生活领域</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{counts.areas}</div>
            <p className="text-xs text-muted-foreground">
              {counts.areas > 0 ? '最近有归档领域' : '暂无归档领域'}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Badge className="para-resource">R</Badge>
              归档资源
            </CardTitle>
            <CardDescription>不再相关的资源文件</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{counts.resources}</div>
            <p className="text-xs text-muted-foreground">
              {counts.resources > 0 ? '最近有归档资源' : '暂无归档资源'}
            </p>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
```
